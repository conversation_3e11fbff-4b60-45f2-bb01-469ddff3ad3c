# Token Library - Keycloak 集成文档

## 文档概览

本文档集合提供了 Token Library 项目中 Keycloak 身份认证集成的完整指南，包括代码实现、部署配置和故障排除。

## 文档结构

### 📋 [Keycloak 集成指南](./keycloak-integration.md)

- **概述**: 项目架构和认证流程
- **环境配置**: 本地和生产环境变量设置
- **核心代码**: KeycloakService、AuthProvider、API 路由实现
- **中间件配置**: 反向代理头信息处理
- **认证保护**: API 和页面级别的访问控制

### ⚙️ [Keycloak 配置指南](./keycloak-setup.md)

- **客户端配置**: 开发和生产环境客户端设置
- **Realm 配置**: 安全策略和用户管理
- **角色权限**: 用户角色和权限分配
- **主题自定义**: 登录页面品牌定制
- **监控维护**: 事件审计和性能优化

### 🚀 [部署指南](./deployment-guide.md)

- **本地开发**: 环境准备和配置步骤
- **生产部署**: 服务器要求和配置
- **反向代理**: Nginx 和 Apache 配置示例
- **进程管理**: PM2 配置和管理
- **SSL 配置**: HTTPS 证书和安全设置

### 🔧 [故障排除指南](./troubleshooting.md)

- **常见问题**: 认证、代理、环境变量问题解决
- **调试技巧**: 日志记录和网络请求分析
- **性能优化**: Cookie、缓存和连接池配置
- **安全最佳实践**: 环境变量、Cookie 和 HTTPS 安全

### ⚡ [快速参考卡片](./quick-reference.md)

- **环境变量**: 开发和生产环境配置速查
- **常用命令**: 开发、部署、监控命令集合
- **API 端点**: 认证和应用功能接口列表
- **故障排除**: 常见错误和解决方案速查
- **安全检查**: 部署前安全检查清单

## 快速开始

### 1. 本地开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd token-library

# 安装依赖
npm install

# 配置环境变量
cp .env.local.example .env.local
# 编辑 .env.local 文件，设置 Keycloak 配置

# 启动开发服务器
npm run dev
```

### 2. 关键环境变量

```env
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=dev
KEYCLOAK_CLIENT_ID=token-library-dev
KEYCLOAK_CLIENT_SECRET=<your-secret>
NEXTAUTH_URL=http://localhost:9032
```

### 3. Keycloak 客户端配置

在 Keycloak 管理控制台中：

1. 创建新客户端，ID 为 `token-library-dev`
2. 设置访问类型为 `confidential`
3. 启用标准流
4. 配置重定向 URI: `http://localhost:9032/api/auth/callback`

## 技术架构

### 认证流程

```
用户 → 登录页面 → Keycloak → 授权码 → Token 交换 → 用户会话
```

### 核心组件

- **KeycloakService**: PKCE 流程处理
- **AuthProvider**: React 认证上下文
- **Middleware**: 反向代理支持
- **API Routes**: 登录、回调、登出处理

### 安全特性

- PKCE (Proof Key for Code Exchange) 流程
- HttpOnly Cookies 存储令牌
- CSRF 保护
- 安全头设置
- HTTPS 强制（生产环境）

## 部署架构

### 本地开发

```
浏览器 → Next.js Dev Server (localhost:9032) → Keycloak
```

### 生产环境

```
浏览器 → Nginx/Apache → Next.js App (localhost:9032) → Keycloak
```

## 重要注意事项

### 🔒 安全考虑

1. **环境变量**: 永远不要在代码中硬编码敏感信息
2. **HTTPS**: 生产环境必须使用 HTTPS
3. **Cookie 安全**: 生产环境启用 secure 和 httpOnly
4. **密钥管理**: 定期轮换 client secret

### 🌐 反向代理配置

1. **Host 头**: 确保正确传递 `X-Forwarded-Host`
2. **协议检测**: 设置 `X-Forwarded-Proto` 头
3. **SSL 终止**: 在代理层处理 SSL
4. **超时设置**: 配置适当的代理超时

### 🐛 常见问题

1. **重定向 URI 不匹配**: 检查 Keycloak 客户端配置
2. **PKCE 验证失败**: 检查 cookie 配置和过期时间
3. **Host 头错误**: 验证反向代理配置
4. **环境变量未加载**: 检查文件名和格式

## 监控和维护

### 日志监控

- 应用日志: `/logs/` 目录
- Nginx 日志: `/var/log/nginx/`
- PM2 日志: `pm2 logs`

### 健康检查

```bash
# 检查应用状态
curl -f http://localhost:9032/api/health

# 检查 Keycloak 连接
curl -f https://keycloak.techexpresser.com/realms/dev/.well-known/openid_configuration
```

### 性能监控

- 响应时间监控
- 内存使用情况
- CPU 使用率
- 数据库连接数

## 支持和贡献

### 获取帮助

1. 查看故障排除指南
2. 检查项目 Issues
3. 联系开发团队

### 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交 Pull Request
4. 更新相关文档

## 版本历史

- **v1.0.0**: 初始 Keycloak 集成
- **v1.1.0**: 添加 PKCE 支持
- **v1.2.0**: 反向代理优化
- **v1.3.0**: 安全增强

---

**注意**: 本文档会随着项目发展持续更新。建议定期查看最新版本。
