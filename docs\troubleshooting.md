# 故障排除指南

## 常见问题

### 1. 认证相关问题

#### 问题：重定向 URI 不匹配
**错误信息**: `Invalid redirect_uri`

**原因**: Keycloak 客户端配置中的重定向 URI 与应用实际使用的不匹配

**解决方案**:
1. 检查 Keycloak 客户端配置中的有效重定向 URI
2. 确保包含以下 URI：
   - 本地开发: `http://localhost:9032/api/auth/callback`
   - 生产环境: `https://token-library.techexpresser.com/api/auth/callback`

#### 问题：PKCE 验证失败
**错误信息**: `PKCE verification failed`

**原因**: 
- Code verifier 丢失或过期
- Cookie 配置问题
- 会话状态不一致

**解决方案**:
```typescript
// 检查 cookie 配置
cookieStore.set("pkce_code_verifier", codeVerifier, {
  httpOnly: true,
  secure: process.env.NODE_ENV === "production", // 生产环境必须为 true
  sameSite: "lax", // 重要：允许跨站点请求
  maxAge: 600, // 10 分钟有效期
})
```

#### 问题：用户信息获取失败
**错误信息**: `Failed to get user info`

**原因**: 
- Access token 无效或过期
- Keycloak 用户信息端点配置错误
- 网络连接问题

**解决方案**:
1. 检查 access token 是否有效
2. 验证 Keycloak URL 和 realm 配置
3. 检查网络连接和防火墙设置

### 2. 反向代理问题

#### 问题：Host 头信息不正确
**错误信息**: 应用无法正确识别域名

**原因**: 反向代理没有正确传递 host 头信息

**解决方案**:
```nginx
# Nginx 配置
proxy_set_header Host $host;
proxy_set_header X-Forwarded-Host $host;
proxy_set_header X-Forwarded-Proto $scheme;
```

```apache
# Apache 配置
ProxyPreserveHost On
RequestHeader set X-Forwarded-Host "token-library.techexpresser.com"
RequestHeader set X-Forwarded-Proto "https"
```

#### 问题：HTTPS 重定向循环
**原因**: 应用无法正确检测 HTTPS 协议

**解决方案**:
1. 确保反向代理设置了正确的协议头：
```nginx
proxy_set_header X-Forwarded-Proto $scheme;
```

2. 在中间件中正确处理协议检测：
```typescript
const forwardedProto = request.headers.get('x-forwarded-proto') || 
                      request.headers.get('x-forwarded-protocol') ||
                      (request.headers.get('x-forwarded-ssl') === 'on' ? 'https' : 'http')
```

### 3. 环境变量问题

#### 问题：环境变量未加载
**错误信息**: `KEYCLOAK_URL is undefined`

**解决方案**:
1. 检查 `.env.local` 文件是否存在且格式正确
2. 确保没有多余的空格或引号
3. 重启开发服务器
4. 验证环境变量加载：
```typescript
console.log('Environment check:', {
  KEYCLOAK_URL: process.env.KEYCLOAK_URL,
  KEYCLOAK_REALM: process.env.KEYCLOAK_REALM,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL
})
```

### 4. Cookie 问题

#### 问题：Cookie 无法设置或读取
**原因**: 
- 安全设置不当
- SameSite 策略限制
- 域名不匹配

**解决方案**:
```typescript
// 开发环境
cookieStore.set("token", value, {
  httpOnly: true,
  secure: false, // 本地开发使用 HTTP
  sameSite: "lax",
  maxAge: 3600
})

// 生产环境
cookieStore.set("token", value, {
  httpOnly: true,
  secure: true, // HTTPS 必须
  sameSite: "lax",
  maxAge: 3600,
  domain: ".techexpresser.com" // 如果需要跨子域
})
```

## 调试技巧

### 1. 启用详细日志

在相关 API 路由中添加调试日志：

```typescript
export async function GET(request: NextRequest) {
  console.log('=== DEBUG INFO ===')
  console.log('Headers:', Object.fromEntries(request.headers.entries()))
  console.log('URL:', request.url)
  console.log('Environment:', {
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NODE_ENV: process.env.NODE_ENV
  })
  console.log('==================')
  
  // 处理逻辑...
}
```

### 2. 检查网络请求

使用浏览器开发者工具：
1. 打开 Network 标签
2. 清除缓存并刷新页面
3. 检查认证相关的请求和响应
4. 查看 Cookie 设置和传递

### 3. Keycloak 日志检查

在 Keycloak 管理控制台中：
1. 进入 Events 页面
2. 查看登录/登出事件
3. 检查错误详情

### 4. 验证 JWT Token

使用 [jwt.io](https://jwt.io) 解码和验证 JWT token：
1. 复制 access_token
2. 在 jwt.io 中粘贴
3. 检查 payload 和过期时间

## 性能优化

### 1. Cookie 优化

```typescript
// 减少 cookie 大小，只存储必要信息
const minimalUserInfo = {
  id: userInfo.sub,
  name: userInfo.name,
  email: userInfo.email
}

cookieStore.set("user_info", JSON.stringify(minimalUserInfo), {
  httpOnly: true,
  secure: process.env.NODE_ENV === "production",
  sameSite: "lax",
  maxAge: tokenData.expires_in,
})
```

### 2. 缓存策略

```typescript
// 在内存中缓存用户信息（开发环境）
const userCache = new Map()

async function getCachedUser(userId: string) {
  if (userCache.has(userId)) {
    return userCache.get(userId)
  }
  
  const user = await fetchUserFromKeycloak(userId)
  userCache.set(userId, user)
  
  // 设置过期时间
  setTimeout(() => userCache.delete(userId), 5 * 60 * 1000) // 5分钟
  
  return user
}
```

### 3. 连接池配置

```typescript
// 为 Keycloak API 调用配置连接池
const agent = new https.Agent({
  keepAlive: true,
  maxSockets: 10,
  timeout: 30000
})

const response = await fetch(keycloakUrl, {
  agent,
  // 其他选项...
})
```

## 安全最佳实践

### 1. 环境变量安全

- 永远不要在代码中硬编码敏感信息
- 使用强随机密钥作为 client secret
- 定期轮换密钥
- 在生产环境中使用环境变量管理工具

### 2. Cookie 安全

```typescript
// 生产环境 cookie 配置
const secureCookieOptions = {
  httpOnly: true,
  secure: true,
  sameSite: "strict" as const,
  maxAge: 3600,
  path: "/",
}
```

### 3. HTTPS 强制

```typescript
// 中间件中强制 HTTPS
export function middleware(request: NextRequest) {
  if (process.env.NODE_ENV === 'production' && 
      !request.headers.get('x-forwarded-proto')?.includes('https')) {
    return NextResponse.redirect(
      `https://${request.headers.get('host')}${request.nextUrl.pathname}`
    )
  }
  
  return NextResponse.next()
}
```

### 4. 输入验证

```typescript
// 验证回调参数
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const code = searchParams.get("code")
  const state = searchParams.get("state")
  
  // 验证必需参数
  if (!code || typeof code !== 'string' || code.length > 1000) {
    return NextResponse.json({ error: "Invalid code" }, { status: 400 })
  }
  
  // 验证 state 参数（如果使用）
  if (state && (typeof state !== 'string' || state.length > 100)) {
    return NextResponse.json({ error: "Invalid state" }, { status: 400 })
  }
  
  // 继续处理...
}
```
