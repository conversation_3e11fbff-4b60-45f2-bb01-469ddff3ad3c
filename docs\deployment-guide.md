# 部署指南

## 本地开发环境

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd token-library

# 安装依赖
npm install

# 复制环境变量文件
cp .env.local.example .env.local
```

### 2. 配置 .env.local

```env
# Keycloak Configuration
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=dev
KEYCLOAK_CLIENT_ID=token-library-dev
KEYCLOAK_CLIENT_SECRET=WHm6ldIGhb0u02m3VCxZOjAxEjGMg23p
KEYCLOAK_ISSUER=https://keycloak.techexpresser.com/realms/dev

# Application Configuration
PORT=9032
NEXTAUTH_URL=http://localhost:9032
```

### 3. 启动开发服务器

```bash
# 开发模式
npm run dev

# 访问应用
# http://localhost:9032
```

### 4. Keycloak 客户端配置

在 Keycloak 管理控制台中配置客户端：

1. **客户端 ID**: `token-library-dev`
2. **客户端协议**: `openid-connect`
3. **访问类型**: `confidential`
4. **标准流启用**: `ON`
5. **有效重定向 URI**: 
   - `http://localhost:9032/api/auth/callback`
   - `http://localhost:9032/*`
6. **Web Origins**: `http://localhost:9032`

## 生产环境部署

### 1. 服务器要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+
- **Node.js**: 18.x 或更高版本
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间
- **网络**: 443 端口（HTTPS）和 80 端口（HTTP 重定向）

### 2. 反向代理配置

#### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name token-library.techexpresser.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name token-library.techexpresser.com;

    # SSL 证书配置
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # 代理配置
    location / {
        proxy_pass http://127.0.0.1:9032;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 静态文件缓存
    location /_next/static/ {
        proxy_pass http://127.0.0.1:9032;
        proxy_cache_valid 200 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### Apache 配置示例

```apache
<VirtualHost *:80>
    ServerName token-library.techexpresser.com
    Redirect permanent / https://token-library.techexpresser.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName token-library.techexpresser.com
    
    # SSL 配置
    SSLEngine on
    SSLCertificateFile /path/to/ssl/certificate.crt
    SSLCertificateKeyFile /path/to/ssl/private.key
    SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    
    # 代理配置
    ProxyPreserveHost On
    ProxyPass / http://127.0.0.1:9032/
    ProxyPassReverse / http://127.0.0.1:9032/
    
    # 设置代理头
    ProxyPassReverse / http://127.0.0.1:9032/
    ProxyPassReverseMatch ^(/.*) http://127.0.0.1:9032$1
    
    # 头信息传递
    ProxyAddHeaders On
    RequestHeader set X-Forwarded-Proto "https"
    RequestHeader set X-Forwarded-Host "token-library.techexpresser.com"
</VirtualHost>
```

### 3. 生产环境变量

创建 `.env.production` 文件：

```env
# Keycloak Configuration
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=production
KEYCLOAK_CLIENT_ID=token-library-prod
KEYCLOAK_CLIENT_SECRET=<production-secret>
KEYCLOAK_ISSUER=https://keycloak.techexpresser.com/realms/production

# Application Configuration
PORT=9032
NEXTAUTH_URL=https://token-library.techexpresser.com
NODE_ENV=production

# 安全配置
NEXTAUTH_SECRET=<random-secret-key>
```

### 4. 构建和部署

```bash
# 构建应用
npm run build

# 启动生产服务器
npm start

# 或使用 PM2 进程管理器
npm install -g pm2
pm2 start npm --name "token-library" -- start
pm2 save
pm2 startup
```

### 5. PM2 配置文件

创建 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [{
    name: 'token-library',
    script: 'npm',
    args: 'start',
    cwd: '/path/to/token-library',
    env: {
      NODE_ENV: 'production',
      PORT: 9032
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
```

启动：
```bash
pm2 start ecosystem.config.js
```
