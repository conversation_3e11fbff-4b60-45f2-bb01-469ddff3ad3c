# 快速参考卡片

## 环境变量速查

### 本地开发 (.env.local)
```env
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=dev
KEYCLOAK_CLIENT_ID=token-library-dev
KEYCLOAK_CLIENT_SECRET=WHm6ldIGhb0u02m3VCxZOjAxEjGMg23p
KEYCLOAK_ISSUER=https://keycloak.techexpresser.com/realms/dev
PORT=9032
NEXTAUTH_URL=http://localhost:9032
```

### 生产环境 (.env.production)
```env
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=production
KEYCLOAK_CLIENT_ID=token-library-prod
KEYCLOAK_CLIENT_SECRET=<production-secret>
KEYCLOAK_ISSUER=https://keycloak.techexpresser.com/realms/production
PORT=9032
NEXTAUTH_URL=https://token-library.techexpresser.com
NODE_ENV=production
```

## 常用命令

### 开发命令
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建应用
npm run build

# 启动生产服务器
npm start
```

### PM2 命令
```bash
# 启动应用
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs token-library

# 重启应用
pm2 restart token-library

# 停止应用
pm2 stop token-library

# 删除应用
pm2 delete token-library
```

### Nginx 命令
```bash
# 测试配置
nginx -t

# 重新加载配置
nginx -s reload

# 重启 Nginx
systemctl restart nginx

# 查看状态
systemctl status nginx
```

## API 端点

### 认证相关
```
GET  /api/auth/login     - 发起登录
GET  /api/auth/callback  - 登录回调
POST /api/auth/logout    - 登出
GET  /api/auth/me        - 获取当前用户信息
```

### 应用功能
```
GET  /api/tokens         - 获取令牌列表
POST /api/tokens         - 创建新令牌
PUT  /api/tokens/[id]    - 更新令牌
DELETE /api/tokens/[id]  - 删除令牌
POST /api/tokens/fetch-models - 获取模型列表
```

## Keycloak 配置速查

### 客户端设置
```
Client ID: token-library-dev / token-library-prod
Access Type: confidential
Standard Flow: ON
Direct Access Grants: OFF
Valid Redirect URIs: 
  - http://localhost:9032/* (开发)
  - https://token-library.techexpresser.com/* (生产)
```

### 重要 URL
```
开发环境:
- Keycloak: https://keycloak.techexpresser.com
- 应用: http://localhost:9032
- 回调: http://localhost:9032/api/auth/callback

生产环境:
- Keycloak: https://keycloak.techexpresser.com
- 应用: https://token-library.techexpresser.com
- 回调: https://token-library.techexpresser.com/api/auth/callback
```

## 故障排除速查

### 常见错误及解决方案

#### 1. Invalid redirect_uri
```bash
# 检查 Keycloak 客户端配置
# 确保重定向 URI 正确设置
```

#### 2. PKCE verification failed
```bash
# 检查 cookie 配置
# 确保 sameSite: "lax"
# 检查 secure 设置（生产环境为 true）
```

#### 3. Host header 错误
```bash
# 检查 Nginx 配置
proxy_set_header Host $host;
proxy_set_header X-Forwarded-Host $host;
proxy_set_header X-Forwarded-Proto $scheme;
```

#### 4. 环境变量未加载
```bash
# 检查文件名和位置
ls -la .env*

# 重启开发服务器
npm run dev
```

### 调试命令

#### 检查服务状态
```bash
# 检查应用
curl -f http://localhost:9032/api/auth/me

# 检查 Keycloak
curl -f https://keycloak.techexpresser.com/realms/dev/.well-known/openid_configuration

# 检查 Nginx
curl -I https://token-library.techexpresser.com
```

#### 查看日志
```bash
# 应用日志
pm2 logs token-library

# Nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 系统日志
journalctl -u nginx -f
```

## 安全检查清单

### 开发环境
- [ ] .env.local 文件存在且配置正确
- [ ] Keycloak 客户端配置正确
- [ ] 重定向 URI 包含 localhost:9032
- [ ] Cookie secure 设置为 false

### 生产环境
- [ ] 环境变量通过安全方式设置
- [ ] HTTPS 证书有效
- [ ] Nginx/Apache 配置正确
- [ ] 防火墙规则配置
- [ ] Cookie secure 设置为 true
- [ ] 安全头设置正确

## 性能优化速查

### Cookie 优化
```typescript
// 最小化 cookie 大小
const minimalUserInfo = {
  id: userInfo.sub,
  name: userInfo.name,
  email: userInfo.email
}
```

### 缓存配置
```nginx
# 静态文件缓存
location /_next/static/ {
  expires 1y;
  add_header Cache-Control "public, immutable";
}
```

### 连接池
```typescript
// HTTP 连接池
const agent = new https.Agent({
  keepAlive: true,
  maxSockets: 10
})
```

## 监控指标

### 关键指标
- 响应时间: < 200ms
- 错误率: < 1%
- 可用性: > 99.9%
- 内存使用: < 80%
- CPU 使用: < 70%

### 监控命令
```bash
# 系统资源
htop
free -h
df -h

# 网络连接
netstat -tulpn | grep :9032
ss -tulpn | grep :9032

# 进程状态
ps aux | grep node
pm2 monit
```

## 联系信息

### 开发团队
- 技术支持: <EMAIL>
- 项目仓库: [GitHub Repository]
- 文档更新: [Documentation Repository]

### 外部服务
- Keycloak 管理: https://keycloak.techexpresser.com/admin
- 服务器监控: [Monitoring Dashboard]
- 日志分析: [Log Analysis Tool]

---

**提示**: 将此页面加入书签以便快速查阅常用信息。
