# Keycloak 配置指南

## 概述

本指南详细说明如何在 Keycloak 中配置客户端以支持 Token Library 应用的身份认证。

## Keycloak 服务器信息

- **服务器地址**: `https://keycloak.techexpresser.com`
- **开发 Realm**: `dev`
- **生产 Realm**: `production`

## 客户端配置

### 1. 创建客户端

#### 开发环境客户端

1. 登录 Keycloak 管理控制台
2. 选择 `dev` realm
3. 导航到 `Clients` → `Create`
4. 填写基本信息：
   - **Client ID**: `token-library-dev`
   - **Client Protocol**: `openid-connect`
   - **Root URL**: `http://localhost:9032`

#### 生产环境客户端

1. 选择 `production` realm
2. 导航到 `Clients` → `Create`
3. 填写基本信息：
   - **Client ID**: `token-library-prod`
   - **Client Protocol**: `openid-connect`
   - **Root URL**: `https://token-library.techexpresser.com`

### 2. 客户端设置配置

#### Settings 标签页

```
Client ID: token-library-dev (或 token-library-prod)
Name: Token Library Application
Description: Token management application with Keycloak authentication
Enabled: ON
Consent Required: OFF
Client Protocol: openid-connect
Access Type: confidential
Standard Flow Enabled: ON
Implicit Flow Enabled: OFF
Direct Access Grants Enabled: OFF
Service Accounts Enabled: OFF
Authorization Enabled: OFF
```

#### 重定向 URI 配置

**开发环境**:
```
Valid Redirect URIs:
- http://localhost:9032/*
- http://localhost:9032/api/auth/callback

Web Origins:
- http://localhost:9032
```

**生产环境**:
```
Valid Redirect URIs:
- https://token-library.techexpresser.com/*
- https://token-library.techexpresser.com/api/auth/callback

Web Origins:
- https://token-library.techexpresser.com
```

#### 高级设置

```
Access Token Lifespan: 5 Minutes
Client Session Idle: 30 Minutes
Client Session Max: 12 Hours
Client Offline Session Idle: 30 Days
Client Offline Session Max: 60 Days

Proof Key for Code Exchange Code Challenge Method: S256
```

### 3. 客户端凭据

#### Credentials 标签页

1. **Client Authenticator**: `Client Id and Secret`
2. **Secret**: 系统生成的密钥（复制保存到环境变量）

**重要**: 妥善保管 Client Secret，不要在代码中硬编码。

### 4. 客户端作用域配置

#### Client Scopes 标签页

确保以下作用域已分配：

**Default Client Scopes**:
- `openid`
- `profile`
- `email`
- `roles`
- `web-origins`

**Optional Client Scopes**:
- `address`
- `phone`
- `offline_access`

### 5. 映射器配置

#### Mappers 标签页

添加自定义映射器以确保正确的用户信息传递：

##### 用户名映射器
```
Name: username
Mapper Type: User Property
Property: username
Token Claim Name: preferred_username
Claim JSON Type: String
Add to ID token: ON
Add to access token: ON
Add to userinfo: ON
```

##### 邮箱映射器
```
Name: email
Mapper Type: User Property
Property: email
Token Claim Name: email
Claim JSON Type: String
Add to ID token: ON
Add to access token: ON
Add to userinfo: ON
```

##### 全名映射器
```
Name: full name
Mapper Type: User's full name
Token Claim Name: name
Add to ID token: ON
Add to access token: ON
Add to userinfo: ON
```

## Realm 配置

### 1. Realm 设置

#### General 标签页

```
Realm name: dev (或 production)
Display name: Development Environment (或 Production Environment)
HTML Display name: <h1>Token Library - Dev</h1>
Frontend URL: https://keycloak.techexpresser.com
Require SSL: external requests
```

#### Login 标签页

```
User registration: OFF (根据需要调整)
Edit username: OFF
Forgot password: ON
Remember me: ON
Verify email: ON (生产环境推荐)
Login with email: ON
Duplicate emails: OFF
```

#### Security Defenses 标签页

```
Headers:
- X-Frame-Options: DENY
- Content-Security-Policy: frame-src 'self'; frame-ancestors 'self'; object-src 'none';
- X-Content-Type-Options: nosniff
- X-Robots-Tag: none
- X-XSS-Protection: 1; mode=block
- Strict-Transport-Security: max-age=31536000; includeSubDomains

Brute Force Detection:
- Enabled: ON
- Max Login Failures: 5
- Wait Increment: 60 seconds
- Quick Login Check: 1000 milliseconds
- Minimum Quick Login Wait: 60 seconds
- Max Wait: 900 seconds
- Failure Reset Time: 12 hours
```

### 2. 用户管理

#### 创建测试用户（开发环境）

1. 导航到 `Users` → `Add user`
2. 填写用户信息：
   ```
   Username: testuser
   Email: <EMAIL>
   First Name: Test
   Last Name: User
   Email Verified: ON
   Enabled: ON
   ```
3. 设置密码：
   - 导航到 `Credentials` 标签页
   - 设置临时密码
   - 要求用户首次登录时更改密码

#### 用户属性

可以添加自定义用户属性：
```
department: IT
role: developer
team: backend
```

### 3. 角色和权限

#### Realm 角色

创建应用相关的角色：
```
- token-admin: 令牌管理员
- token-user: 普通用户
- token-viewer: 只读用户
```

#### 客户端角色

在客户端中创建特定角色：
```
- admin: 应用管理员
- user: 普通用户
```

#### 角色映射

为用户分配适当的角色：
1. 选择用户
2. 导航到 `Role Mappings` 标签页
3. 分配 Realm 角色和客户端角色

## 主题自定义

### 1. 登录页面主题

可以自定义 Keycloak 登录页面以匹配应用品牌：

1. 导航到 `Realm Settings` → `Themes`
2. 设置登录主题：
   ```
   Login Theme: keycloak (或自定义主题)
   Account Theme: keycloak
   Admin Console Theme: keycloak
   Email Theme: keycloak
   ```

### 2. 自定义 CSS

创建自定义主题目录：
```
/opt/keycloak/themes/token-library/
├── login/
│   ├── resources/
│   │   └── css/
│   │       └── login.css
│   └── theme.properties
```

## 事件和审计

### 1. 事件配置

#### Events Config 标签页

```
Save Events: ON
Expiration: 7 days
Events to save:
- LOGIN
- LOGOUT
- REGISTER
- UPDATE_PROFILE
- UPDATE_PASSWORD
- LOGIN_ERROR
- LOGOUT_ERROR

Admin Events Settings:
Save Admin Events: ON
Include Representation: OFF
```

### 2. 事件监听器

可以配置事件监听器以集成外部系统：
```
Event Listeners:
- jboss-logging
- email (如果配置了邮件)
```

## 备份和恢复

### 1. 配置导出

定期导出 Realm 配置：
```bash
# 导出 realm 配置
/opt/keycloak/bin/standalone.sh \
  -Djboss.socket.binding.port-offset=100 \
  -Dkeycloak.migration.action=export \
  -Dkeycloak.migration.provider=singleFile \
  -Dkeycloak.migration.realmName=dev \
  -Dkeycloak.migration.file=/tmp/dev-realm.json
```

### 2. 配置导入

```bash
# 导入 realm 配置
/opt/keycloak/bin/standalone.sh \
  -Dkeycloak.migration.action=import \
  -Dkeycloak.migration.provider=singleFile \
  -Dkeycloak.migration.file=/tmp/dev-realm.json \
  -Dkeycloak.migration.strategy=OVERWRITE_EXISTING
```

## 监控和维护

### 1. 健康检查

```bash
# 检查 Keycloak 状态
curl -f https://keycloak.techexpresser.com/auth/realms/dev/.well-known/openid_configuration

# 检查管理接口
curl -f https://keycloak.techexpresser.com/auth/admin/
```

### 2. 日志监控

监控 Keycloak 日志文件：
```bash
tail -f /opt/keycloak/standalone/log/server.log
```

关注以下日志模式：
- 认证失败
- 配置错误
- 性能问题
- 安全事件

### 3. 性能调优

#### JVM 参数优化

```bash
export JAVA_OPTS="-Xms1024m -Xmx2048m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m"
```

#### 数据库连接池

```xml
<datasource jndi-name="java:jboss/datasources/KeycloakDS" pool-name="KeycloakDS">
    <connection-url>************************************</connection-url>
    <driver>postgresql</driver>
    <pool>
        <min-pool-size>10</min-pool-size>
        <max-pool-size>50</max-pool-size>
        <prefill>true</prefill>
    </pool>
    <security>
        <user-name>keycloak</user-name>
        <password>password</password>
    </security>
</datasource>
```

## 安全最佳实践

### 1. 网络安全
- 使用 HTTPS
- 限制管理接口访问
- 配置防火墙规则
- 使用反向代理

### 2. 认证安全
- 启用暴力破解保护
- 配置强密码策略
- 启用双因素认证（如需要）
- 定期审查用户权限

### 3. 配置安全
- 定期备份配置
- 使用版本控制管理配置
- 限制管理员权限
- 监控配置变更

---

**注意**: 本配置指南基于 Keycloak 的特定版本。不同版本的界面和选项可能有所差异。
