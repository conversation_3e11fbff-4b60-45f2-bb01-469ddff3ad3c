# Keycloak 集成完整指南

## 概述

本项目使用 Keycloak 作为身份认证提供者，实现了基于 PKCE (Proof Key for Code Exchange) 的 OAuth 2.0 授权码流程。支持本地开发和生产环境部署，包括反向代理配置。

## 架构设计

### 认证流程

1. 用户访问受保护页面
2. 重定向到 Keycloak 登录页面
3. 用户在 Keycloak 完成认证
4. Keycloak 回调应用并返回授权码
5. 应用使用授权码交换访问令牌
6. 获取用户信息并建立会话

### 核心组件

- **KeycloakService**: 处理 PKCE 流程和 API 调用
- **AuthProvider**: React Context 提供认证状态
- **Middleware**: 处理反向代理头信息
- **API Routes**: 处理登录、回调、登出等操作

## 环境配置

### 本地开发环境 (.env.local)

```env
# Keycloak Configuration
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=dev
KEYCLOAK_CLIENT_ID=token-library-dev
KEYCLOAK_CLIENT_SECRET=WHm6ldIGhb0u02m3VCxZOjAxEjGMg23p
KEYCLOAK_ISSUER=https://keycloak.techexpresser.com/realms/dev

# Application Configuration
PORT=9032
NEXTAUTH_URL=http://localhost:9032
```

### 生产环境配置

```env
# Keycloak Configuration
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=production
KEYCLOAK_CLIENT_ID=token-library-prod
KEYCLOAK_CLIENT_SECRET=<production-secret>
KEYCLOAK_ISSUER=https://keycloak.techexpresser.com/realms/production

# Application Configuration
PORT=9032
NEXTAUTH_URL=https://token-library.techexpresser.com
NODE_ENV=production
```

## 核心代码实现

### 1. Keycloak 服务类 (app/lib/keycloak.ts)

```typescript
export class KeycloakService {
  private static readonly KEYCLOAK_URL = process.env.KEYCLOAK_URL!;
  private static readonly REALM = process.env.KEYCLOAK_REALM!;
  private static readonly CLIENT_ID = process.env.KEYCLOAK_CLIENT_ID!;
  private static readonly CLIENT_SECRET = process.env.KEYCLOAK_CLIENT_SECRET!;

  // PKCE 代码验证器生成
  private static generateCodeVerifier(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode.apply(null, Array.from(array)))
      .replace(/\+/g, "-")
      .replace(/\//g, "_")
      .replace(/=/g, "");
  }

  // PKCE 代码挑战生成
  private static async generateCodeChallenge(
    verifier: string
  ): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(verifier);
    const digest = await crypto.subtle.digest("SHA-256", data);
    return btoa(
      String.fromCharCode.apply(null, Array.from(new Uint8Array(digest)))
    )
      .replace(/\+/g, "-")
      .replace(/\//g, "_")
      .replace(/=/g, "");
  }

  // 获取登录 URL
  static async getLoginUrl(
    redirectUri: string
  ): Promise<{ url: string; codeVerifier: string }> {
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = await this.generateCodeChallenge(codeVerifier);

    const params = new URLSearchParams({
      client_id: this.CLIENT_ID,
      redirect_uri: redirectUri,
      response_type: "code",
      scope: "openid profile email",
      code_challenge_method: "S256",
      code_challenge: codeChallenge,
    });

    return {
      url: `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/auth?${params}`,
      codeVerifier,
    };
  }

  // 交换授权码获取令牌
  static async exchangeCodeForToken(
    code: string,
    redirectUri: string,
    codeVerifier: string
  ) {
    const tokenUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/token`;

    const response = await fetch(tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        client_id: this.CLIENT_ID,
        client_secret: this.CLIENT_SECRET,
        code,
        redirect_uri: redirectUri,
        code_verifier: codeVerifier,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to exchange code for token");
    }

    return response.json();
  }

  // 获取用户信息
  static async getUserInfo(accessToken: string): Promise<KeycloakUser> {
    const userInfoUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/userinfo`;

    const response = await fetch(userInfoUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to get user info");
    }

    const userInfo = await response.json();

    return {
      id: userInfo.sub,
      name: userInfo.name || userInfo.preferred_username,
      email: userInfo.email,
      preferred_username: userInfo.preferred_username,
    };
  }

  // 登出
  static async logout(refreshToken: string): Promise<void> {
    const logoutUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/logout`;

    await fetch(logoutUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: this.CLIENT_ID,
        client_secret: this.CLIENT_SECRET,
        refresh_token: refreshToken,
      }),
    });
  }
}
```

### 2. 认证上下文 (app/lib/auth-context.tsx)

```typescript
interface User {
  id: string;
  name: string;
  email: string;
  preferred_username: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const hasCheckedAuthRef = useRef(false);

  useEffect(() => {
    if (!hasCheckedAuthRef.current) {
      hasCheckedAuthRef.current = true;
      checkAuth();
    }
  }, []);

  const checkAuth = async () => {
    try {
      const response = await fetch("/api/auth/me");
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      } else if (response.status === 401) {
        setUser(null);
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await fetch("/api/auth/logout", { method: "POST" });
      setUser(null);
      window.location.href = "/";
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);
```

### 3. API 路由实现

#### 登录路由 (app/api/auth/login/route.ts)

```typescript
export async function GET(request: NextRequest) {
  // 强制使用环境变量，不依赖request.nextUrl.origin
  const baseUrl =
    process.env.NEXTAUTH_URL || "https://token-library.techexpresser.com";
  const redirectUri = `${baseUrl}/api/auth/callback`;

  // Enhanced debug logging
  console.log("=== LOGIN DEBUG INFO ===");
  console.log("NEXTAUTH_URL:", process.env.NEXTAUTH_URL);
  console.log("request.nextUrl.origin:", request.nextUrl.origin);
  console.log("Final baseUrl:", baseUrl);
  console.log("Final redirectUri:", redirectUri);

  // Generate PKCE parameters
  const { url: loginUrl, codeVerifier } = await KeycloakService.getLoginUrl(
    redirectUri
  );

  // Store code verifier in a secure cookie for the callback
  const cookieStore = cookies();
  cookieStore.set("pkce_code_verifier", codeVerifier, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: 600, // 10 minutes
  });

  // 正常重定向到Keycloak
  return NextResponse.redirect(loginUrl);
}
```

#### 回调路由 (app/api/auth/callback/route.ts)

```typescript
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  const baseUrl =
    process.env.NEXTAUTH_URL || "https://token-library.techexpresser.com";

  if (!code) {
    return NextResponse.redirect(`${baseUrl}/auth/signin?error=no_code`);
  }

  const cookieStore = cookies();
  const codeVerifierCookie = cookieStore.get("pkce_code_verifier");

  if (!codeVerifierCookie) {
    return NextResponse.redirect(`${baseUrl}/auth/signin?error=no_verifier`);
  }

  try {
    const redirectUri = `${baseUrl}/api/auth/callback`;
    const tokenData = await KeycloakService.exchangeCodeForToken(
      code,
      redirectUri,
      codeVerifierCookie.value
    );
    const userInfo = await KeycloakService.getUserInfo(tokenData.access_token);

    // Clear the code verifier cookie
    cookieStore.delete("pkce_code_verifier");

    // Set secure cookies
    cookieStore.set("access_token", tokenData.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.expires_in,
    });

    cookieStore.set("refresh_token", tokenData.refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.refresh_expires_in,
    });

    cookieStore.set("user_info", JSON.stringify(userInfo), {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.expires_in,
    });

    return NextResponse.redirect(`${baseUrl}/`);
  } catch (error) {
    console.error("Callback error:", error);
    return NextResponse.redirect(
      `${baseUrl}/auth/signin?error=callback_failed`
    );
  }
}
```

#### 登出路由 (app/api/auth/logout/route.ts)

```typescript
export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  const refreshToken = cookieStore.get("refresh_token");

  if (refreshToken) {
    try {
      await KeycloakService.logout(refreshToken.value);
    } catch (error) {
      console.error("Keycloak logout error:", error);
    }
  }

  // Clear all auth cookies
  cookieStore.delete("access_token");
  cookieStore.delete("refresh_token");
  cookieStore.delete("user_info");

  return NextResponse.json({ success: true });
}
```

#### 用户信息路由 (app/api/auth/me/route.ts)

```typescript
export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const userInfo = cookieStore.get("user_info");

  if (!userInfo) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  try {
    const user = JSON.parse(userInfo.value);
    return NextResponse.json(user);
  } catch (error) {
    return NextResponse.json({ error: "Invalid user data" }, { status: 401 });
  }
}
```

### 4. 中间件配置 (middleware.ts)

```typescript
import { NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
  // Clone the request headers
  const requestHeaders = new Headers(request.headers);

  // Get the forwarded host from various possible headers
  const forwardedHost =
    request.headers.get("x-forwarded-host") ||
    request.headers.get("x-original-host") ||
    request.headers.get("host");

  // Get the forwarded protocol
  const forwardedProto =
    request.headers.get("x-forwarded-proto") ||
    request.headers.get("x-forwarded-protocol") ||
    (request.headers.get("x-forwarded-ssl") === "on" ? "https" : "http");

  // If we have forwarded headers, update the request
  if (forwardedHost && forwardedHost !== "localhost:9032") {
    // Set the correct host header
    requestHeaders.set("host", forwardedHost);

    // Log for debugging
    console.log("=== MIDDLEWARE DEBUG ===");
    console.log("Original host:", request.headers.get("host"));
    console.log("Forwarded host:", forwardedHost);
    console.log("Forwarded proto:", forwardedProto);
    console.log("Request URL:", request.url);
    console.log("========================");
  }

  // Continue with the request
  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
```

### 5. Next.js 配置 (next.config.mjs)

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // 添加反向代理支持
  async headers() {
    return [
      {
        source: "/api/:path*",
        headers: [
          {
            key: "X-Forwarded-Host",
            value: "token-library.techexpresser.com",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
```

## 认证保护实现

### API 路由保护

在需要认证的 API 路由中使用以下模式：

```typescript
async function getAuthenticatedUser() {
  const cookieStore = cookies();
  const userInfo = cookieStore.get("user_info");

  if (!userInfo) {
    return null;
  }

  try {
    return JSON.parse(userInfo.value);
  } catch {
    return null;
  }
}

export async function POST(request: NextRequest) {
  const user = await getAuthenticatedUser();

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // 处理已认证的请求
  // ...
}
```

### 页面级保护

使用 AuthProvider 和 useAuth hook：

```typescript
function ProtectedPage() {
  const { user, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <Navigate to="/auth/signin" />;
  }

  return <div>Protected content for {user.name}</div>;
}
```
